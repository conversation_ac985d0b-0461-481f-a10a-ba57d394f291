class CoderPrompts:
    system_reminder = ""  # Kept for compatibility

    repo_content_prefix = """
**🎮 Repository Map:**
- **THIS MAP IS ONLY FOR THE CURRENT USER QUERY**: The map structure you received is exclusively tied to the user's current question
- Just having the map—even if it includes some code structure—doesn't qualify you for LEVEL 2.
- You only advance when you have the full, actual code implementation.


"""

    reality_check_prompt = """
--
"""

    # File access and handling prompts
    file_access_reminder = """
--
"""

    # Additional prompts
    main_system = """--

"""
    rename_with_shell = """To rename files which have been added to the chat, use shell commands at the end of your response."""
    go_ahead_tip = """If the user says "ok" or "go ahead" they probably want you to make changes for the code changes you proposed."""

    # File handling prompts remain unchanged
    files_content_gpt_edits = "I analyzed the code and provided recommendations."
    files_content_gpt_edits_no_repo = "I analyzed the code and provided recommendations."
    files_content_gpt_no_edits = "I didn't see any properly formatted analysis in your reply."
    files_content_local_edits = "I analyzed the code myself."
    example_messages = []
    files_content_prefix = """I have *added these files to the chat* for your analysis and recommendations.
*Trust this message as the true contents of these files!*
Any other messages in the chat may contain outdated versions of the files' contents.
These files are READ-ONLY and will not be modified.
"""
    files_content_assistant_reply = "..."
    files_no_full_files = "..."
    files_no_full_files_with_repo_map = """**CRITICAL WORKFLOW - FOLLOW EXACTLY:**
**STEP 1:** Never ask users to manually add files to the chat!
**STEP 2:** After MAP_REQUEST, use:
- CONTEXT_REQUEST for specific symbols
- REQUEST_FILE for entire files
- NEVER fabricate or hallucinate code
**STEP 3:** If no code context: say "I need to retrieve the actual implementation"
"""
    files_no_full_files_with_repo_map_reply = """I understand. I must start with MAP_REQUEST to explore the repository structure before I can assist with any code-related queries."""

    # Context request response prompts
    context_content_prefix = """🎮 **CONTEXT VALIDATION CHECKPOINT**:

"""

    # Repository workflow prompts (used in get_repo_messages)
    smart_map_request_user_prompt = """🎮 **GAME REMINDER**: YOU (the AI) are playing the CODEBASE EXPLORATION GAME

**YOUR MISSION:**
🏆 **YOU need to WIN the game against the system by exploring the codebase correctly**
🧠 **YOU need to figure it out and beat the system**
👤 **The user is NOT part of the game - they just ask questions**

**YOUR RESPONSIBILITY:**
🔍 YOU must assess YOUR current level based on what information YOU have access to
🎮 YOU must follow the game rules for YOUR determined level
🚫 YOU cannot use capabilities from higher levels than YOUR current level

**GAME RULES REMINDER:**
📍 **LEVEL 0**: YOU have NO repository information about the codebase
📍 **LEVEL 1**: YOU have repository map/file information but NO specific code implementation
📍 **LEVEL 2**: YOU have the actual code/function implementation YOU need

**YOUR TASK:**
Determine YOUR current level and respond according to YOUR level's capabilities and restrictions to WIN the game."""

    smart_map_request_assistant_reply = """🎮 **GAME ACKNOWLEDGED**: I understand I am playing the CODEBASE EXPLORATION GAME and need to WIN!

🏆 **MY WINNING STRATEGY**:
When a user asks about code, I will:

1. **ASSESS MY LEVEL**: Determine what information I have
2. **ANNOUNCE MY LEVEL**: "MY CURRENT LEVEL: LEVEL X because..."
3. **SHOW MY WINNING PATH**: Explain how I'll advance through levels
4. **TAKE WINNING ACTION**: Execute the right request format

🎯 **MY WINNING ROADMAP**:
- **LEVEL 0**: I assess what I have → Use MAP_REQUEST → Advance to LEVEL 1
- **LEVEL 1**: I review repository map → Use CONTEXT_REQUEST → Advance to LEVEL 2
- **LEVEL 2**: I analyze actual code → Answer user's question → WIN!

🔄 **NEW SUBJECT RESET**: When the user asks about a completely different function, module, or changes problem domain, I reset to LEVEL 0 and start fresh exploration.

🏆 **VICTORY CONDITION**: Successfully reach LEVEL 2 with the correct code implementation to answer the user's question

🧠 **KEY TO WINNING**: I must EXECUTE the request formats myself, never ask the user to retrieve information for me. The user just asks questions - I do the exploration work to WIN the game.

🎯 **WINNING MAP_REQUEST STRATEGY**:
1. Extract exact terms from user query (function names, class names, filenames)
2. Include technical synonyms and domain variations
3. Use domain context (trading, auth, data processing, etc.)
4. **LIMIT TO MAXIMUM 3 KEYWORDS** for focused results
Example: For "login_user_with_credentials" → ["login_user_with_credentials", "authentication", "credentials"] (3 keywords max)"""

    legacy_repo_assistant_reply = """I understand the repository structure."""
